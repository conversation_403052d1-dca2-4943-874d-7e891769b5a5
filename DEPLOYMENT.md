# SSM框架订单查询系统部署指南

**学号：2022591854 姓名：何晨 班级：软工B2207**

## 一、环境准备

### 1. 软件环境要求
- **JDK**: 1.8 或以上版本
- **MySQL**: 8.0 或以上版本
- **Tomcat**: 9.0 或以上版本
- **Maven**: 3.6 或以上版本
- **IDE**: IntelliJ IDEA 或 Eclipse（可选）

### 2. 环境变量配置
确保以下环境变量已正确配置：
- `JAVA_HOME`: JDK安装路径
- `MAVEN_HOME`: Maven安装路径
- `CATALINA_HOME`: Tomcat安装路径

## 二、数据库配置

### 1. 创建数据库
```sql
-- 连接MySQL数据库
mysql -u root -p

-- 创建数据库
CREATE DATABASE IF NOT EXISTS db_mybatis DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 2. 执行初始化脚本
```sql
-- 使用数据库
USE db_mybatis;

-- 执行项目中的初始化脚本
source /path/to/project/src/main/resources/init.sql;
```

### 3. 验证数据
```sql
-- 查看表结构
DESC orders;

-- 查看测试数据
SELECT * FROM orders;
```

## 三、项目配置

### 1. 修改数据库连接配置
编辑 `src/main/resources/db.properties` 文件：
```properties
jdbc.driver=com.mysql.cj.jdbc.Driver
jdbc.url=****************************************************************************************************************************************************
jdbc.username=你的数据库用户名
jdbc.password=你的数据库密码
```

### 2. 检查Maven配置
确保 `pom.xml` 中的依赖版本与你的环境兼容。

## 四、项目构建

### 1. 使用Maven构建
```bash
# 进入项目根目录
cd /path/to/project

# 清理并编译项目
mvn clean compile

# 运行测试（可选）
mvn test

# 打包项目
mvn clean package
```

### 2. 构建成功标志
构建成功后，会在 `target` 目录下生成 `ssm-orders-1.0-SNAPSHOT.war` 文件。

## 五、部署到Tomcat

### 1. 方式一：直接部署WAR包
```bash
# 复制WAR包到Tomcat的webapps目录
cp target/ssm-orders-1.0-SNAPSHOT.war $CATALINA_HOME/webapps/

# 启动Tomcat
$CATALINA_HOME/bin/startup.sh  # Linux/Mac
$CATALINA_HOME/bin/startup.bat # Windows
```

### 2. 方式二：解压部署
```bash
# 在webapps目录下创建项目目录
mkdir $CATALINA_HOME/webapps/ssm-orders

# 解压WAR包到项目目录
cd $CATALINA_HOME/webapps/ssm-orders
jar -xvf ../ssm-orders-1.0-SNAPSHOT.war
```

### 3. 方式三：IDE集成部署
如果使用IDE（如IntelliJ IDEA），可以：
1. 配置Tomcat服务器
2. 添加项目到服务器
3. 直接运行调试

## 六、访问测试

### 1. 启动验证
- 启动Tomcat后，检查日志文件 `$CATALINA_HOME/logs/catalina.out`
- 确保没有错误信息，项目正常启动

### 2. 功能测试
访问以下URL进行功能测试：

```
# 首页
http://localhost:8080/ssm-orders-1.0-SNAPSHOT/

# 查看所有订单
http://localhost:8080/ssm-orders-1.0-SNAPSHOT/orders/list

# 条件查询页面
http://localhost:8080/ssm-orders-1.0-SNAPSHOT/orders/search
```

### 3. 测试用例
1. **查询所有订单**：应该显示5条测试数据
2. **按订单号查询**：输入"20221201"应该返回1条记录
3. **按会员ID查询**：输入"1"应该返回2条记录
4. **按产品名称查询**：输入"手机"应该返回3条记录

## 七、常见问题解决

### 1. 数据库连接问题
```
错误：Could not create connection to database server
解决：
- 检查MySQL服务是否启动
- 验证数据库连接参数
- 确认数据库用户权限
```

### 2. 编码问题
```
错误：中文显示乱码
解决：
- 确保数据库字符集为utf8mb4
- 检查web.xml中的字符编码过滤器
- 验证JSP页面编码设置
```

### 3. 404错误
```
错误：页面无法访问
解决：
- 检查URL路径是否正确
- 验证控制器映射路径
- 确认JSP文件位置
```

### 4. 500错误
```
错误：服务器内部错误
解决：
- 查看Tomcat日志文件
- 检查Spring配置文件
- 验证数据库连接
```

## 八、性能优化建议

### 1. 数据库优化
- 为常用查询字段添加索引
- 配置合适的连接池参数
- 定期清理日志文件

### 2. 应用优化
- 启用Spring缓存
- 配置合适的JVM参数
- 使用CDN加速静态资源

## 九、安全配置

### 1. 数据库安全
- 使用专用数据库用户
- 限制数据库用户权限
- 定期更新数据库密码

### 2. 应用安全
- 配置HTTPS
- 添加输入验证
- 实现访问控制

## 十、维护说明

### 1. 日志管理
- 定期清理应用日志
- 监控错误日志
- 配置日志轮转

### 2. 备份策略
- 定期备份数据库
- 备份应用配置文件
- 制定恢复计划

---

**部署完成后，请访问系统首页验证所有功能是否正常工作。**

**如有问题，请检查日志文件并参考本文档的故障排除部分。**
