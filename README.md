# SSM框架订单查询系统

**学号：2022591854 姓名：何晨 班级：软工B2207**

## 项目简介

本项目基于SSM框架（Spring + Spring MVC + MyBatis）开发，实现了订单查询功能。系统支持多种查询方式，包括查询所有订单、按订单号查询、按会员ID查询和按产品名称模糊查询。

## 技术栈

- **Spring Framework 5.3.21** - 依赖注入和AOP
- **Spring MVC 5.3.21** - Web层框架
- **MyBatis 3.5.10** - 持久层框架
- **MySQL 8.0** - 数据库
- **Druid** - 数据库连接池
- **JSP + JSTL** - 视图层技术
- **Maven** - 项目构建工具

## 项目结构

```
src/
├── main/
│   ├── java/
│   │   └── com/example/
│   │       ├── entity/          # 实体类
│   │       │   └── Orders.java
│   │       ├── mapper/          # 数据访问层
│   │       │   └── OrdersMapper.java
│   │       ├── service/         # 业务逻辑层
│   │       │   ├── OrdersService.java
│   │       │   └── impl/
│   │       │       └── OrdersServiceImpl.java
│   │       └── controller/      # 控制层
│   │           ├── HomeController.java
│   │           └── OrdersController.java
│   ├── resources/               # 配置文件
│   │   ├── spring-context.xml   # Spring配置
│   │   ├── spring-mvc.xml       # Spring MVC配置
│   │   ├── mybatis-config.xml   # MyBatis配置
│   │   ├── db.properties        # 数据库配置
│   │   ├── logback.xml          # 日志配置
│   │   └── init.sql             # 数据库初始化脚本
│   └── webapp/                  # Web资源
│       ├── WEB-INF/
│       │   └── web.xml          # Web应用配置
│       └── views/               # JSP页面
│           ├── index.jsp        # 首页
│           ├── orders/          # 订单相关页面
│           │   ├── search.jsp   # 查询页面
│           │   └── list.jsp     # 列表页面
│           └── error/           # 错误页面
│               ├── 404.jsp
│               └── 500.jsp
```

## 功能特性

### 1. 订单查询功能
- **查询所有订单**：显示数据库中的所有订单记录
- **按订单号查询**：根据订单号精确查询单个订单
- **按会员ID查询**：查询指定会员的所有订单
- **按产品名称查询**：根据产品名称进行模糊查询

### 2. 用户界面
- 响应式设计，支持不同屏幕尺寸
- 美观的表格展示订单数据
- 友好的查询条件输入界面
- 完善的错误页面处理

### 3. 系统特性
- 完整的SSM框架整合
- 数据库连接池管理
- 事务管理支持
- 日志记录功能
- 字符编码处理

## 数据库设计

### orders表结构
```sql
CREATE TABLE orders (
    number VARCHAR(50) NOT NULL COMMENT '订单号',
    member_id VARCHAR(50) NOT NULL COMMENT '会员ID',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    count INT NOT NULL COMMENT '数量',
    money DOUBLE NOT NULL COMMENT '金额',
    PRIMARY KEY (number)
);
```

### 测试数据
```sql
INSERT INTO orders(number, member_id, product_name, count, money) VALUES
('20221201', '1', '手机', 1, 3500),
('20221202', '3', '手机、净化器', 2, 4500),
('20221203', '2', '净化器', 1, 1000),
('20221204', '1', '空调', 1, 2500),
('20221205', '2', '手机、空调', 2, 6000);
```

## 部署说明

### 1. 环境要求
- JDK 8+
- MySQL 8.0+
- Tomcat 9.0+
- Maven 3.6+

### 2. 数据库配置
1. 创建数据库：`CREATE DATABASE db_mybatis;`
2. 执行初始化脚本：`src/main/resources/init.sql`
3. 修改数据库连接配置：`src/main/resources/db.properties`

### 3. 项目部署
1. 使用Maven构建项目：`mvn clean package`
2. 将生成的war包部署到Tomcat
3. 启动Tomcat服务器
4. 访问：`http://localhost:8080/项目名/`

## 访问地址

- **首页**：`/`
- **查询所有订单**：`/orders/list`
- **条件查询页面**：`/orders/search`

## 开发者信息

- **学号**：2022591854
- **姓名**：何晨
- **班级**：软工B2207
- **开发时间**：2024年

## 项目特色

1. **完整的SSM框架整合**：展示了Spring、Spring MVC和MyBatis的完整整合过程
2. **多种查询方式**：支持精确查询和模糊查询
3. **美观的用户界面**：使用CSS美化页面，提供良好的用户体验
4. **完善的错误处理**：包含404和500错误页面
5. **规范的代码结构**：遵循MVC设计模式，代码结构清晰
6. **详细的注释说明**：每个类和方法都有详细的注释
