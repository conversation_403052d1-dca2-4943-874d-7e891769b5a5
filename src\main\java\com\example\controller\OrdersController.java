package com.example.controller;

import com.example.entity.Orders;
import com.example.service.OrdersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 订单控制器类
 * 学号：2022591854 姓名：何晨 班级：软工B2207
 */
@Controller
@RequestMapping("/orders")
public class OrdersController {
    
    @Autowired
    private OrdersService ordersService;
    
    /**
     * 显示订单查询页面
     */
    @GetMapping("/search")
    public String searchPage() {
        return "orders/search";
    }
    
    /**
     * 查询所有订单
     */
    @GetMapping("/list")
    public String findAll(Model model) {
        List<Orders> ordersList = ordersService.findAll();
        model.addAttribute("ordersList", ordersList);
        model.addAttribute("searchType", "all");
        return "orders/list";
    }
    
    /**
     * 根据条件查询订单
     */
    @PostMapping("/search")
    public String search(@RequestParam("searchType") String searchType,
                        @RequestParam("searchValue") String searchValue,
                        Model model) {
        List<Orders> ordersList = null;
        Orders singleOrder = null;
        
        switch (searchType) {
            case "number":
                singleOrder = ordersService.findByNumber(searchValue);
                if (singleOrder != null) {
                    ordersList = List.of(singleOrder);
                }
                break;
            case "memberId":
                ordersList = ordersService.findByMemberId(searchValue);
                break;
            case "productName":
                ordersList = ordersService.findByProductName(searchValue);
                break;
            default:
                ordersList = ordersService.findAll();
                break;
        }
        
        model.addAttribute("ordersList", ordersList);
        model.addAttribute("searchType", searchType);
        model.addAttribute("searchValue", searchValue);
        return "orders/list";
    }
}
