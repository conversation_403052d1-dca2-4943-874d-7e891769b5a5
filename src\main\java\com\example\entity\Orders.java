package com.example.entity;

/**
 * 订单实体类
 * 学号：2022591854 姓名：何晨 班级：软工B2207
 */
public class Orders {
    private String number;      // 订单号
    private String memberId;    // 会员ID
    private String productName; // 产品名称
    private Integer count;      // 数量
    private Double money;       // 金额

    public Orders() {
    }

    public Orders(String number, String memberId, String productName, Integer count, Double money) {
        this.number = number;
        this.memberId = memberId;
        this.productName = productName;
        this.count = count;
        this.money = money;
    }

    public String getNumber() {
        return number;
    }

    public void setNumber(String number) {
        this.number = number;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getCount() {
        return count;
    }

    public void setCount(Integer count) {
        this.count = count;
    }

    public Double getMoney() {
        return money;
    }

    public void setMoney(Double money) {
        this.money = money;
    }

    @Override
    public String toString() {
        return "Orders{" +
                "number='" + number + '\'' +
                ", memberId='" + memberId + '\'' +
                ", productName='" + productName + '\'' +
                ", count=" + count +
                ", money=" + money +
                '}';
    }
}
