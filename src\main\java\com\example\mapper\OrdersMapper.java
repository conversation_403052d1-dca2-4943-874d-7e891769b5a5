package com.example.mapper;

import com.example.entity.Orders;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 订单数据访问层接口
 * 学号：2022591854 姓名：何晨 班级：软工B2207
 */
@Mapper
public interface OrdersMapper {
    
    /**
     * 查询所有订单
     * @return 订单列表
     */
    @Select("SELECT number, member_id as memberId, product_name as productName, count, money FROM orders")
    List<Orders> findAll();
    
    /**
     * 根据订单号查询订单
     * @param number 订单号
     * @return 订单信息
     */
    @Select("SELECT number, member_id as memberId, product_name as productName, count, money FROM orders WHERE number = #{number}")
    Orders findByNumber(@Param("number") String number);
    
    /**
     * 根据会员ID查询订单
     * @param memberId 会员ID
     * @return 订单列表
     */
    @Select("SELECT number, member_id as memberId, product_name as productName, count, money FROM orders WHERE member_id = #{memberId}")
    List<Orders> findByMemberId(@Param("memberId") String memberId);
    
    /**
     * 根据产品名称模糊查询订单
     * @param productName 产品名称
     * @return 订单列表
     */
    @Select("SELECT number, member_id as memberId, product_name as productName, count, money FROM orders WHERE product_name LIKE CONCAT('%', #{productName}, '%')")
    List<Orders> findByProductName(@Param("productName") String productName);
}
