package com.example.service;

import com.example.entity.Orders;

import java.util.List;

/**
 * 订单业务逻辑层接口
 * 学号：2022591854 姓名：何晨 班级：软工B2207
 */
public interface OrdersService {
    
    /**
     * 查询所有订单
     * @return 订单列表
     */
    List<Orders> findAll();
    
    /**
     * 根据订单号查询订单
     * @param number 订单号
     * @return 订单信息
     */
    Orders findByNumber(String number);
    
    /**
     * 根据会员ID查询订单
     * @param memberId 会员ID
     * @return 订单列表
     */
    List<Orders> findByMemberId(String memberId);
    
    /**
     * 根据产品名称模糊查询订单
     * @param productName 产品名称
     * @return 订单列表
     */
    List<Orders> findByProductName(String productName);
}
