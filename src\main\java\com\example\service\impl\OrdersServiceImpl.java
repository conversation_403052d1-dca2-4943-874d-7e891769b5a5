package com.example.service.impl;

import com.example.entity.Orders;
import com.example.mapper.OrdersMapper;
import com.example.service.OrdersService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 订单业务逻辑层实现类
 * 学号：2022591854 姓名：何晨 班级：软工B2207
 */
@Service
public class OrdersServiceImpl implements OrdersService {
    
    @Autowired
    private OrdersMapper ordersMapper;
    
    @Override
    public List<Orders> findAll() {
        return ordersMapper.findAll();
    }
    
    @Override
    public Orders findByNumber(String number) {
        return ordersMapper.findByNumber(number);
    }
    
    @Override
    public List<Orders> findByMemberId(String memberId) {
        return ordersMapper.findByMemberId(memberId);
    }
    
    @Override
    public List<Orders> findByProductName(String productName) {
        return ordersMapper.findByProductName(productName);
    }
}
