<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>SSM框架订单管理系统</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .info {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
            border-left: 4px solid #2196F3;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 10px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #1976D2;
        }
        .btn-group {
            text-align: center;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>订单查询系统（学号：2022591854，姓名：何晨，班级：软工B2207）</h1>
        
        <div class="info">
            <h3>系统说明</h3>
            <p>本系统基于SSM框架（Spring + Spring MVC + MyBatis）开发，实现了订单查询功能。</p>
            <p>支持以下查询方式：</p>
            <ul>
                <li>查询所有订单</li>
                <li>根据订单号查询</li>
                <li>根据会员ID查询</li>
                <li>根据产品名称模糊查询</li>
            </ul>
        </div>
        
        <div class="btn-group">
            <a href="${pageContext.request.contextPath}/orders/list" class="btn">查看所有订单</a>
            <a href="${pageContext.request.contextPath}/orders/search" class="btn">条件查询订单</a>
        </div>
    </div>
</body>
</html>
