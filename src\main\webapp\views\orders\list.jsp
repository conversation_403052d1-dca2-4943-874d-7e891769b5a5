<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>订单列表</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .search-info {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #555;
        }
        tr:hover {
            background-color: #f5f5f5;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background-color: #2196F3;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            margin: 5px;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #1976D2;
        }
        .btn-secondary {
            background-color: #666;
        }
        .btn-secondary:hover {
            background-color: #555;
        }
        .btn-group {
            text-align: center;
        }
        .no-data {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 40px;
        }
        .money {
            color: #e91e63;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>订单列表（学号：2022591854，姓名：何晨，班级：软工B2207）</h1>
        
        <c:if test="${not empty searchType and searchType != 'all'}">
            <div class="search-info">
                <strong>查询条件：</strong>
                <c:choose>
                    <c:when test="${searchType == 'number'}">按订单号查询</c:when>
                    <c:when test="${searchType == 'memberId'}">按会员ID查询</c:when>
                    <c:when test="${searchType == 'productName'}">按产品名称查询</c:when>
                </c:choose>
                - "${searchValue}"
            </div>
        </c:if>
        
        <c:choose>
            <c:when test="${not empty ordersList}">
                <table>
                    <thead>
                        <tr>
                            <th>订单号</th>
                            <th>会员ID</th>
                            <th>产品名称</th>
                            <th>数量</th>
                            <th>金额</th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="order" items="${ordersList}">
                            <tr>
                                <td>${order.number}</td>
                                <td>${order.memberId}</td>
                                <td>${order.productName}</td>
                                <td>${order.count}</td>
                                <td class="money">¥<fmt:formatNumber value="${order.money}" pattern="#,##0.00"/></td>
                            </tr>
                        </c:forEach>
                    </tbody>
                </table>
                
                <div class="search-info">
                    <strong>查询结果：</strong>共找到 ${ordersList.size()} 条订单记录
                </div>
            </c:when>
            <c:otherwise>
                <div class="no-data">
                    没有找到符合条件的订单记录
                </div>
            </c:otherwise>
        </c:choose>
        
        <div class="btn-group">
            <a href="${pageContext.request.contextPath}/orders/search" class="btn">重新查询</a>
            <a href="${pageContext.request.contextPath}/orders/list" class="btn btn-secondary">查看所有订单</a>
            <a href="${pageContext.request.contextPath}/" class="btn btn-secondary">返回首页</a>
        </div>
    </div>
</body>
</html>
