<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>订单查询</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        select, input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        .btn {
            background-color: #2196F3;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        .btn:hover {
            background-color: #1976D2;
        }
        .btn-secondary {
            background-color: #666;
        }
        .btn-secondary:hover {
            background-color: #555;
        }
        .btn-group {
            text-align: center;
            margin-top: 30px;
        }
        .info {
            background-color: #e8f4fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border-left: 4px solid #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>订单查询（学号：2022591854，姓名：何晨，班级：软工B2207）</h1>
        
        <div class="info">
            <p>请选择查询类型并输入相应的查询条件</p>
        </div>
        
        <form action="${pageContext.request.contextPath}/orders/search" method="post">
            <div class="form-group">
                <label for="searchType">查询类型：</label>
                <select id="searchType" name="searchType" required>
                    <option value="">请选择查询类型</option>
                    <option value="number">按订单号查询</option>
                    <option value="memberId">按会员ID查询</option>
                    <option value="productName">按产品名称查询</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="searchValue">查询条件：</label>
                <input type="text" id="searchValue" name="searchValue" 
                       placeholder="请输入查询条件" required>
            </div>
            
            <div class="btn-group">
                <button type="submit" class="btn">查询</button>
                <a href="${pageContext.request.contextPath}/orders/list" class="btn btn-secondary">查看所有订单</a>
                <a href="${pageContext.request.contextPath}/" class="btn btn-secondary">返回首页</a>
            </div>
        </form>
    </div>
</body>
</html>
