package com.example.service;

import com.example.entity.Orders;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 * 订单服务测试类
 * 学号：2022591854 姓名：何晨 班级：软工B2207
 */
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = {"classpath:spring-context.xml"})
public class OrdersServiceTest {
    
    @Autowired
    private OrdersService ordersService;
    
    @Test
    public void testFindAll() {
        List<Orders> ordersList = ordersService.findAll();
        System.out.println("查询到的订单数量：" + (ordersList != null ? ordersList.size() : 0));
        if (ordersList != null) {
            for (Orders order : ordersList) {
                System.out.println(order);
            }
        }
    }
    
    @Test
    public void testFindByNumber() {
        Orders order = ordersService.findByNumber("20221201");
        System.out.println("根据订单号查询结果：" + order);
    }
    
    @Test
    public void testFindByMemberId() {
        List<Orders> ordersList = ordersService.findByMemberId("1");
        System.out.println("会员1的订单数量：" + (ordersList != null ? ordersList.size() : 0));
        if (ordersList != null) {
            for (Orders order : ordersList) {
                System.out.println(order);
            }
        }
    }
    
    @Test
    public void testFindByProductName() {
        List<Orders> ordersList = ordersService.findByProductName("手机");
        System.out.println("包含'手机'的订单数量：" + (ordersList != null ? ordersList.size() : 0));
        if (ordersList != null) {
            for (Orders order : ordersList) {
                System.out.println(order);
            }
        }
    }
}
