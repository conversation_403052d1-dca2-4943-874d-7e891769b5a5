-- 数据库初始化脚本
-- 学号：2022591854 姓名：何晨 班级：软工B2207

-- 创建数据库
CREATE DATABASE IF NOT EXISTS db_ssm DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用数据库
USE db_ssm;

-- 删除已存在的表
DROP TABLE IF EXISTS orders;

-- 创建订单表
CREATE TABLE orders (
    number VARCHAR(50) NOT NULL COMMENT '订单号',
    member_id VARCHAR(50) NOT NULL COMMENT '会员ID',
    product_name VARCHAR(200) NOT NULL COMMENT '产品名称',
    count INT NOT NULL COMMENT '数量',
    money DOUBLE NOT NULL COMMENT '金额',
    PRIMARY KEY (number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='订单表';

-- 插入测试数据
INSERT INTO orders(number, member_id, product_name, count, money) VALUES
('20221201', '1', '手机', 1, 3500),
('20221202', '3', '手机、净化器', 2, 4500),
('20221203', '2', '净化器', 1, 1000),
('20221204', '1', '空调', 1, 2500),
('20221205', '2', '手机、空调', 2, 6000);

-- 查询所有数据
SELECT * FROM orders;
